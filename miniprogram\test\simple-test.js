/**
 * 简单的基础产品对比测试脚本
 * 对比产品：华为 Mate 70 Pro 和 苹果iPhone 16 Pro
 */

// 配置后端服务器地址
const BASE_URL = 'http://localhost:3000/api/v1';

/**
 * 保存数据到JSON文件
 */
function saveToJsonFile(data) {
  const filename = 'basic-compare-result.json';
  const jsonData = JSON.stringify(data, null, 2);
  
  // 在小程序环境中
  if (typeof wx !== 'undefined') {
    const fs = wx.getFileSystemManager();
    const filePath = `${wx.env.USER_DATA_PATH}/${filename}`;
    fs.writeFileSync(filePath, jsonData, 'utf8');
    console.log(`✅ 数据已保存到: ${filePath}`);
  } else {
    // 浏览器环境
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    console.log(`✅ 数据已下载: ${filename}`);
  }
}

/**
 * 发送HTTP请求
 */
function sendRequest(url, data) {
  return new Promise((resolve, reject) => {
    if (typeof wx !== 'undefined') {
      // 小程序环境
      wx.request({
        url: url,
        method: 'POST',
        data: data,
        header: { 'Content-Type': 'application/json' },
        success: (res) => resolve(res.data),
        fail: (error) => reject(error)
      });
    } else {
      // 浏览器环境
      fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      .then(response => response.json())
      .then(data => resolve(data))
      .catch(error => reject(error));
    }
  });
}

/**
 * 执行基础对比测试
 */
async function testBasicCompare() {
  console.log('🧪 开始测试基础产品对比接口...');
  console.log('📱 测试产品: 华为 Mate 70 Pro vs 苹果iPhone 16 Pro');

  const requestData = {
    productNames: ['华为 Mate 70 Pro', '苹果iPhone 16 Pro']
  };
  
  const apiUrl = `${BASE_URL}/products/compare-basic`;

  try {
    console.log('📡 正在发送请求...');
    const response = await sendRequest(apiUrl, requestData);
    
    console.log('✅ 请求成功!');
    console.log('📊 响应数据:', response);
    
    // 保存完整响应数据
    saveToJsonFile(response);
    
    console.log('🎉 测试完成，数据已保存!');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 运行测试
 */
function runTest() {
  testBasicCompare();
}

// 如果在浏览器环境中，添加到全局作用域
if (typeof window !== 'undefined') {
  window.testBasicCompare = testBasicCompare;
  window.runTest = runTest;
  console.log('💡 在浏览器控制台中输入 runTest() 开始测试');
}

// 导出函数
module.exports = {
  testBasicCompare,
  runTest
};
