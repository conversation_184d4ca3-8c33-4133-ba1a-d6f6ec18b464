/**
 * 基础产品对比接口测试脚本
 * 测试 /api/v1/products/compare-basic 接口
 * 对比产品：华为 Mate 70 Pro 和 苹果iPhone 16 Pro
 */

const api = require('../utils/api');
const fs = require('fs');
const path = require('path');

/**
 * 保存数据到JSON文件
 * @param {Object} data 要保存的数据
 * @param {String} filename 文件名
 */
function saveToJsonFile(data, filename) {
  try {
    const filePath = path.join(__dirname, filename);
    const jsonData = JSON.stringify(data, null, 2);
    
    // 在小程序环境中使用 wx.getFileSystemManager()
    if (typeof wx !== 'undefined') {
      const fs = wx.getFileSystemManager();
      fs.writeFileSync(filePath, jsonData, 'utf8');
      console.log(`✅ 数据已保存到: ${filename}`);
    } else {
      // Node.js 环境（开发测试用）
      require('fs').writeFileSync(filePath, jsonData, 'utf8');
      console.log(`✅ 数据已保存到: ${filePath}`);
    }
  } catch (error) {
    console.error('❌ 保存文件失败:', error);
  }
}

/**
 * 格式化时间戳
 * @param {Number} timestamp 时间戳
 * @returns {String} 格式化后的时间字符串
 */
function formatTime(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 分析对比数据结构
 * @param {Object} comparisonData 对比数据
 * @returns {Object} 分析结果
 */
function analyzeComparisonData(comparisonData) {
  const analysis = {
    totalCategories: 0,
    totalParameters: 0,
    categoriesDetail: {},
    dataStructure: {}
  };

  if (comparisonData && typeof comparisonData === 'object') {
    const categories = Object.keys(comparisonData);
    analysis.totalCategories = categories.length;
    
    categories.forEach(category => {
      const categoryData = comparisonData[category];
      if (categoryData && typeof categoryData === 'object') {
        const parameters = Object.keys(categoryData);
        analysis.categoriesDetail[category] = {
          parameterCount: parameters.length,
          parameters: parameters
        };
        analysis.totalParameters += parameters.length;
        
        // 分析数据结构
        if (parameters.length > 0) {
          const firstParam = categoryData[parameters[0]];
          if (firstParam && typeof firstParam === 'object') {
            analysis.dataStructure[category] = {
              sampleParameter: parameters[0],
              productKeys: Object.keys(firstParam),
              sampleValues: firstParam
            };
          }
        }
      }
    });
  }

  return analysis;
}

/**
 * 执行基础对比测试
 */
async function testBasicCompare() {
  console.log('🧪 开始测试基础产品对比接口...');
  console.log('📱 测试产品: 华为 Mate 70 Pro vs 苹果iPhone 16 Pro');
  console.log('⏰ 测试时间:', formatTime(Date.now()));
  console.log('');

  const testProducts = ['华为 Mate 70 Pro', '苹果iPhone 16 Pro'];
  const startTime = Date.now();

  try {
    // 调用基础对比API
    console.log('📡 正在调用基础对比API...');
    const response = await api.product.compareProductsBasic(testProducts);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('✅ API调用成功!');
    console.log(`⏱️ 响应时间: ${duration}ms`);
    console.log('');

    // 检查响应结构
    if (response && response.success) {
      console.log('📊 响应数据分析:');
      console.log(`   - 成功状态: ${response.success}`);
      console.log(`   - 响应码: ${response.code}`);
      console.log(`   - 响应消息: ${response.message}`);
      
      if (response.data) {
        const { products, rawComparisonData, notFoundProducts, meta } = response.data;
        
        console.log(`   - 找到产品数量: ${products ? products.length : 0}`);
        console.log(`   - 未找到产品数量: ${notFoundProducts ? notFoundProducts.length : 0}`);
        
        if (notFoundProducts && notFoundProducts.length > 0) {
          console.log(`   - 未找到的产品: ${notFoundProducts.join(', ')}`);
        }
        
        // 分析对比数据
        if (rawComparisonData) {
          const analysis = analyzeComparisonData(rawComparisonData);
          console.log(`   - 参数分类数量: ${analysis.totalCategories}`);
          console.log(`   - 总参数数量: ${analysis.totalParameters}`);
          
          console.log('   - 分类详情:');
          Object.keys(analysis.categoriesDetail).forEach(category => {
            const detail = analysis.categoriesDetail[category];
            console.log(`     * ${category}: ${detail.parameterCount}个参数`);
          });
        }
        
        if (meta) {
          console.log(`   - 元数据版本: ${meta.version}`);
          console.log(`   - 元数据描述: ${meta.description}`);
        }
      }
      
      // 构建完整的测试结果数据
      const testResult = {
        testInfo: {
          testTime: formatTime(Date.now()),
          testProducts: testProducts,
          apiEndpoint: '/api/v1/products/compare-basic',
          responseTime: duration,
          testStatus: 'SUCCESS'
        },
        apiResponse: response,
        dataAnalysis: response.data && response.data.rawComparisonData ? 
          analyzeComparisonData(response.data.rawComparisonData) : null
      };
      
      // 保存完整数据到JSON文件
      const filename = `basic-compare-result-${Date.now()}.json`;
      saveToJsonFile(testResult, filename);
      
      console.log('');
      console.log('🎉 基础对比测试完成!');
      console.log(`📄 完整数据已保存到: ${filename}`);
      
    } else {
      console.error('❌ API响应格式异常:', response);
      
      // 保存错误响应
      const errorResult = {
        testInfo: {
          testTime: formatTime(Date.now()),
          testProducts: testProducts,
          apiEndpoint: '/api/v1/products/compare-basic',
          responseTime: duration,
          testStatus: 'FAILED'
        },
        errorResponse: response,
        errorMessage: 'API响应格式异常'
      };
      
      saveToJsonFile(errorResult, `basic-compare-error-${Date.now()}.json`);
    }
    
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.error('❌ 测试失败:', error);
    console.log(`⏱️ 失败前耗时: ${duration}ms`);
    
    // 保存错误信息
    const errorResult = {
      testInfo: {
        testTime: formatTime(Date.now()),
        testProducts: testProducts,
        apiEndpoint: '/api/v1/products/compare-basic',
        responseTime: duration,
        testStatus: 'ERROR'
      },
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      }
    };
    
    saveToJsonFile(errorResult, `basic-compare-error-${Date.now()}.json`);
  }
}

/**
 * 小程序页面调用入口
 */
function runTest() {
  testBasicCompare();
}

// 导出测试函数
module.exports = {
  testBasicCompare,
  runTest,
  saveToJsonFile,
  analyzeComparisonData
};
