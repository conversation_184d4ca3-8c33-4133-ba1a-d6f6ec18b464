const Joi = require('joi');

/**
 * 产品对比验证规则
 */
const compareProductsSchema = Joi.object({
  productNames: Joi.array()
    .items(
      Joi.string()
        .trim()
        .min(1)
        .max(100)
        .required()
    )
    .min(2)
    .max(5)
    .required()
    .messages({
      'array.base': '产品名称必须是数组格式',
      'array.min': '至少需要提供2个产品进行对比',
      'array.max': '最多支持5个产品同时对比',
      'any.required': '请提供要对比的产品名称列表'
    })
});

/**
 * 产品名称搜索验证规则
 */
const searchProductNamesSchema = Joi.object({
  keyword: Joi.string()
    .trim()
    .min(1)
    .max(50)
    .required()
    .messages({
      'string.empty': '搜索关键词不能为空',
      'string.min': '搜索关键词至少1个字符',
      'string.max': '搜索关键词不能超过50个字符',
      'any.required': '请提供搜索关键词'
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(20)
    .default(10)
    .messages({
      'number.base': '返回数量必须是数字',
      'number.integer': '返回数量必须是整数',
      'number.min': '返回数量至少为1',
      'number.max': '返回数量最多为20'
    }),
  category: Joi.string()
    .valid('phone', 'laptop', 'tablet', 'headphones', 'smartwatch', 'other')
    .allow('')
    .default('')
    .messages({
      'any.only': '产品类别必须是有效的类型（phone, laptop, tablet, headphones, smartwatch, other）'
    })
});

/**
 * 产品库查询验证规则
 */
const queryProductsSchema = Joi.object({
  productType: Joi.string()
    .valid('phone', 'laptop', 'tablet', 'headphones', 'smartwatch', 'other')
    .allow('')
    .default('')
    .messages({
      'any.only': '产品类型必须是有效的类型（phone, laptop, tablet, headphones, smartwatch, other）'
    }),
  brandName: Joi.string()
    .trim()
    .max(50)
    .allow('')
    .default('')
    .messages({
      'string.max': '品牌名称不能超过50个字符'
    }),
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码必须大于0'
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(50)
    .default(20)
    .messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量至少为1',
      'number.max': '每页数量最多为50'
    })
});

/**
 * 产品参数获取验证规则
 */
const getProductParamsSchema = Joi.object({
  productName: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.empty': '产品名称不能为空',
      'string.min': '产品名称至少1个字符',
      'string.max': '产品名称不能超过100个字符',
      'any.required': '请提供产品名称'
    })
});

/**
 * 产品对比V4验证规则 - 基于参数字段提取的智能对比
 */
const compareProductsV4Schema = Joi.object({
  productNames: Joi.array()
    .items(
      Joi.string()
        .trim()
        .min(1)
        .max(100)
        .required()
    )
    .min(2)
    .max(6)
    .required()
    .messages({
      'array.base': '产品名称必须是数组格式',
      'array.min': '至少需要提供2个产品进行对比',
      'array.max': '最多支持6个产品同时对比',
      'any.required': '请提供要对比的产品名称列表'
    })
});

module.exports = {
  compareProductsSchema,
  searchProductNamesSchema,
  queryProductsSchema,
  getProductParamsSchema,
  compareProductsV4Schema
};